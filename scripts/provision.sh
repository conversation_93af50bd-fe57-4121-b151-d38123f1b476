# update && upgrade if needed
# sudo apt update && sudo apt upgrade -y


# create user if needed
# sudo useradd coloraria
# sudo usermod -aG sudo coloraria
# sudo usermod -aG docker coloraria

# create github SSH
# Scan Github SSH public key and add to known_hosts
# ssh-keyscan -H github.com >> ~/.ssh/known_hosts

# SSH config entry to add
# SSH_CONFIG_ENTRY="Host coloraria-repo
#     Hostname github.com
#     User git
#     IdentityFile ~/.ssh/coloraria-github
#     IdentitiesOnly yes"

# # Path to SSH config file
# SSH_CONFIG_FILE="$HOME/.ssh/config"

# # Create .ssh directory if it doesn't exist
# mkdir -p "$HOME/.ssh"

# # Check if the host entry already exists
# if grep -q "Host coloraria-repo" "$SSH_CONFIG_FILE" 2>/dev/null; then
#     echo "Host 'coloraria-repo' already exists in $SSH_CONFIG_FILE"
#     echo "Skipping addition to avoid duplicates."
# else
#     # Add the configuration entry
#     echo "" >> "$SSH_CONFIG_FILE"
#     echo "$SSH_CONFIG_ENTRY" >> "$SSH_CONFIG_FILE"
#     echo "Added coloraria-repo host configuration to $SSH_CONFIG_FILE"
    
#     # Set proper permissions for SSH config file
#     chmod 600 "$SSH_CONFIG_FILE"
#     echo "Set proper permissions (600) for $SSH_CONFIG_FILE"
# fi

# # Verify the addition
# echo ""
# echo "Current SSH config for coloraria-repo:"
# grep -A 4 "Host coloraria-repo" "$SSH_CONFIG_FILE" 2>/dev/null || echo "Entry not found"
# git clone coloraria-repo:proriderless/line-art-generator-v2.git

# setup monitoring service
# mkdir -p ~/.config/systemd/user/
# scp -i ~/.ssh/worker systemctl/posthog-monitor.service coloraria@<server_ip>:~/.config/systemd/user/

# systemctl --user daemon-reload
# systemctl --user enable posthog-monitor.service
# systemctl --user start posthog-monitor.service
# systemctl --user status posthog-monitor.service
# when set as an user service, it'll stopped when user logout. Need to enable linger
# loginctl enable-linger coloraria

# pulling secret - todo
# pushing secret for now
# sudo scp -i ~/.ssh/worker -r letsencrypt scripts traefik coloraria@<server_ip>:line-art-generator-v2/
# scp -i ~/.ssh/worker .env.production coloraria@<server_ip>:line-art-generator-v2/

# setting docker env
docker network create traefik-net



